package com.snct.init;

import com.snct.common.utils.spring.SpringUtils;
import com.snct.device.service.PduDeviceService;
import com.snct.netty.SendService;
import com.snct.serialport.RtxtService2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/5 23:55
 */
@Component
@Order(0)
public class SystemLoad implements CommandLineRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PduDeviceService pduDeviceService;

    @Override
    public void run(String... args) throws Exception {
        logger.info("系统基础功能初始化开始...");

        // 串口监听
        RtxtService2 rtxtService2 = SpringUtils.getBean(RtxtService2.class);
        rtxtService2.handleAllService();

        // 网络传输
        SendService sendService = SpringUtils.getBean(SendService.class);
        sendService.cleanSendParametersMap();
        sendService.renewLaputaIpPort();
        sendService.consumeRedisList();

        logger.info("系统基础功能初始化结束...");
    }
}
