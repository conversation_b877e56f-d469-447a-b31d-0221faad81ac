package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataAws;
import com.snct.system.service.IBuDataAwsService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * AWS气象数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/aws")
public class BuDataAwsController extends BaseController
{
    @Autowired
    private IBuDataAwsService buDataAwsService;

    /**
     * 查询AWS气象数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:aws:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataAws buDataAws)
    {
        startPage();
        List<BuDataAws> list = buDataAwsService.selectBuDataAwsList(buDataAws);
        return getDataTable(list);
    }

    /**
     * 导出AWS气象数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:aws:export')")
    @Log(title = "AWS气象数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataAws buDataAws)
    {
        List<BuDataAws> list = buDataAwsService.selectBuDataAwsList(buDataAws);
        ExcelUtil<BuDataAws> util = new ExcelUtil<BuDataAws>(BuDataAws.class);
        util.exportExcel(response, list, "AWS气象数据数据");
    }

    /**
     * 获取AWS气象数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:aws:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataAwsService.selectBuDataAwsById(id));
    }

    /**
     * 新增AWS气象数据
     */
    @PreAuthorize("@ss.hasPermi('system:aws:add')")
    @Log(title = "AWS气象数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataAws buDataAws)
    {
        return toAjax(buDataAwsService.insertBuDataAws(buDataAws));
    }

    /**
     * 修改AWS气象数据
     */
    @PreAuthorize("@ss.hasPermi('system:aws:edit')")
    @Log(title = "AWS气象数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataAws buDataAws)
    {
        return toAjax(buDataAwsService.updateBuDataAws(buDataAws));
    }

    /**
     * 删除AWS气象数据
     */
    @PreAuthorize("@ss.hasPermi('system:aws:remove')")
    @Log(title = "AWS气象数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataAwsService.deleteBuDataAwsByIds(ids));
    }
}
