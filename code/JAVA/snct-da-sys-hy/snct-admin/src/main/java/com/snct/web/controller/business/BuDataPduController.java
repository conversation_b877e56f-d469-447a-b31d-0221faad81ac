package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataPdu;
import com.snct.system.service.IBuDataPduService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * PDU电源分配单元数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/pdu")
public class BuDataPduController extends BaseController
{
    @Autowired
    private IBuDataPduService buDataPduService;

    /**
     * 查询PDU电源分配单元数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataPdu buDataPdu)
    {
        startPage();
        List<BuDataPdu> list = buDataPduService.selectBuDataPduList(buDataPdu);
        return getDataTable(list);
    }

    /**
     * 导出PDU电源分配单元数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:export')")
    @Log(title = "PDU电源分配单元数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataPdu buDataPdu)
    {
        List<BuDataPdu> list = buDataPduService.selectBuDataPduList(buDataPdu);
        ExcelUtil<BuDataPdu> util = new ExcelUtil<BuDataPdu>(BuDataPdu.class);
        util.exportExcel(response, list, "PDU电源分配单元数据数据");
    }

    /**
     * 获取PDU电源分配单元数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataPduService.selectBuDataPduById(id));
    }

    /**
     * 新增PDU电源分配单元数据
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:add')")
    @Log(title = "PDU电源分配单元数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataPdu buDataPdu)
    {
        return toAjax(buDataPduService.insertBuDataPdu(buDataPdu));
    }

    /**
     * 修改PDU电源分配单元数据
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:edit')")
    @Log(title = "PDU电源分配单元数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataPdu buDataPdu)
    {
        return toAjax(buDataPduService.updateBuDataPdu(buDataPdu));
    }

    /**
     * 删除PDU电源分配单元数据
     */
    @PreAuthorize("@ss.hasPermi('system:pdu:remove')")
    @Log(title = "PDU电源分配单元数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataPduService.deleteBuDataPduByIds(ids));
    }
}
