package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataGps;
import com.snct.system.service.IBuDataGpsService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * GPS数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/gps")
public class BuDataGpsController extends BaseController
{
    @Autowired
    private IBuDataGpsService buDataGpsService;

    /**
     * 查询GPS数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:gps:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataGps buDataGps)
    {
        startPage();
        List<BuDataGps> list = buDataGpsService.selectBuDataGpsList(buDataGps);
        return getDataTable(list);
    }

    /**
     * 导出GPS数据列表
     */
    @PreAuthorize("@ss.hasPermi('system:gps:export')")
    @Log(title = "GPS数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataGps buDataGps)
    {
        List<BuDataGps> list = buDataGpsService.selectBuDataGpsList(buDataGps);
        ExcelUtil<BuDataGps> util = new ExcelUtil<BuDataGps>(BuDataGps.class);
        util.exportExcel(response, list, "GPS数据数据");
    }

    /**
     * 获取GPS数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:gps:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataGpsService.selectBuDataGpsById(id));
    }

    /**
     * 新增GPS数据
     */
    @PreAuthorize("@ss.hasPermi('system:gps:add')")
    @Log(title = "GPS数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataGps buDataGps)
    {
        return toAjax(buDataGpsService.insertBuDataGps(buDataGps));
    }

    /**
     * 修改GPS数据
     */
    @PreAuthorize("@ss.hasPermi('system:gps:edit')")
    @Log(title = "GPS数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataGps buDataGps)
    {
        return toAjax(buDataGpsService.updateBuDataGps(buDataGps));
    }

    /**
     * 删除GPS数据
     */
    @PreAuthorize("@ss.hasPermi('system:gps:remove')")
    @Log(title = "GPS数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataGpsService.deleteBuDataGpsByIds(ids));
    }
}
